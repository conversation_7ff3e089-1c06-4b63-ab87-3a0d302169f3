import { Prop,<PERSON>hem<PERSON>,SchemaFactory } from "@nestjs/mongoose";
import { Document as MongooseDocument } from 'mongoose';


export type UserDocument =MongooseDocument & User;

@Schema({timestamps:true})
export class User{
    @Prop({required:true})
    userName: String;

    @Prop({required:true,unique: true})
    email: String;

    @Prop({required:true})
    password: String;

    @Prop({default:false})
    isVerified: <PERSON><PERSON><PERSON>
    
    @Prop({default:"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS2TgOv9CMmsUzYKCcLGWPvqcpUk6HXp2mnww&s"})
    profilePicUrl: String

    @Prop({enum:["user","veternia","admin"],required:true})
    userType: String
    
    @Prop({required:false})
    phoneNumber: String

    @Prop({default:3})
    freeTrialSlots: number

    @Prop({default:""})
    Specialization: String

    @Prop({})
    Charges: String
    
    @Prop({})
    State: String

    @Prop({})
    Experience: String

    // @Prop({})
    // WorkingHours: String

    @Prop({default : ""})
    about: String
    @Prop({})
    stripeConnectId: string
    @Prop({type:[String]})
    liscenceImages:string[]
    @Prop({default:""})
    hospital: string
    @Prop({
        type: Object,
        default: {
          monday: { available: false, start: '', end: '' },
          tuesday: { available: false, start: '', end: '' },
          wednesday: { available: false, start: '', end: '' },
          thursday: { available: false, start: '', end: '' },
          friday: { available: false, start: '', end: '' },
          saturday: { available: false, start: '', end: '' },
          sunday: { available: false, start: '', end: '' },
        }
      })
      availability: {
        monday: { available: boolean; start: string; end: string };
        tuesday: { available: boolean; start: string; end: string };
        wednesday: { available: boolean; start: string; end: string };
        thursday: { available: boolean; start: string; end: string };
        friday: { available: boolean; start: string; end: string };
        saturday: { available: boolean; start: string; end: string };
        sunday: { available: boolean; start: string; end: string };
      };
      @Prop({default:false})
      approve:Boolean

      @Prop({default:false})
      isComplete:Boolean

      @Prop({default: false})
      blockStatus:Boolean

      // ----- Stripe --------
      @Prop({default: "none"})
      connected_acc_id: string; 

      @Prop({default: "none"})
      connected_external_acc_id: string;

      @Prop({default:false}) 
      payouts_enabled: boolean;

      @Prop({default:0})
      allowed_withdrawl_balance: number;

      // @Prop({})
//required:()=>{return this.userType ==='veternia'}
}

export const UserSchema=SchemaFactory.createForClass(User);