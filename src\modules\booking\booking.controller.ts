import { BookingService } from './booking.service';
import { Controller, UseGuards, Patch, Body, Req,UseInterceptors, UploadedFile,Get, Query,Post  } from '@nestjs/common';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';
import { CreateBookingDto } from './dto/createBooking.dto';
import { Request } from 'express';
import { customerBookingDto } from './dto/gettingCustomerBooking.dto';
import { Param } from '@nestjs/common';
import { JwtVeterniaGuard } from 'src/shared/guards/jwt-veternia';
@Controller('booking')
export class BookingController {
    constructor(private readonly bookingService:BookingService){
    }
  
  @UseGuards(JwtAuthGuard)
  @Post('create')
  async createBooking(
    @Body() dto: CreateBookingDto,
    @Req() req: Request,
  ) {
    const userId = req.user['sub']; 
    return this.bookingService.createBooking(dto, userId);
  }


  @UseGuards(JwtAuthGuard)
  @Patch('set-active/:id')
  async setBookingActive(@Param('id') id: string) {
    return this.bookingService.setBookingActive(id);
  }
  @UseGuards(JwtAuthGuard)
  @Patch('set-incomplete/:id')
  async setBookingIncomplete(@Param('id') id: string) {
    return this.bookingService.setBookingIncomplete(id);
  }
  @UseGuards(JwtAuthGuard)
  @Patch('set-completed/:id')
  async setBookingCompleted(@Param('id') id: string) {
    return this.bookingService.setBookingCompleted(id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('getCustomerBookings')
  async getCustomerBooking(@Query() dto:customerBookingDto,@Req() req: Request){
    const userId=req.user['sub'];
    return this.bookingService.getBookingOfCustomer(dto,userId)
  }
  @UseGuards(JwtVeterniaGuard)
  @Get('getVeterniaBooking')
  async getVeterniabooking(@Query() dto: customerBookingDto,@Req() req:Request){
    const userId=req.user['sub'];
    return this.bookingService.bookingOfVeternia(dto,userId)
  }

  // -------------- Veternia Home Section ----------------------------------
  @UseGuards(JwtVeterniaGuard)
  @Get('get-all-bookings-count')
  async getAllBookingsCount(@Req() req: Request){
    const userId=req.user['sub'];
    return this.bookingService.getAllBookingsCount(userId)
  }

  @UseGuards(JwtVeterniaGuard)
  @Get('get-all-active-bookings-count')
  async getAllActiveBookingsCount(@Req() req: Request){
    const userId=req.user['sub'];
    return this.bookingService.getAllActiveBookingsCount(userId)
  }

  @UseGuards(JwtVeterniaGuard)
  @Get('get-all-completed-bookings-count')
  async getAllCompletedBookingsCount(@Req() req: Request){
    const userId=req.user['sub'];
    return this.bookingService.getAllCompletedBookingsCount(userId)
  }

  @UseGuards(JwtVeterniaGuard)
  @Get('get-all-incompleted-bookings-count')  
  async getAllIncompletedBookingsCount(@Req() req: Request){
    const userId=req.user['sub'];
    return this.bookingService.getAllIncompletedBookingsCount(userId)
  }
}
