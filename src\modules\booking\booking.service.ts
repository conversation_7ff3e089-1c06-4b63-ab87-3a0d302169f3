import { ConflictException, Injectable } from '@nestjs/common';
import { User, UserDocument } from '../users/schemas/user.schema';
import { UsersService } from '../users/users.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { booking, bookingDocument } from './schemas/bookingSchemas';
import { findCharges,calculateCharges } from 'src/shared/utils/calculateCharges';
import { PaymentService } from '../../payment/payment.service';
@Injectable()
export class BookingService {
  constructor(
    private readonly userService: UsersService,
    private readonly paymentService:PaymentService,        
    @InjectModel(User.name) private readonly userModel: Model<UserDocument>,
    @InjectModel(booking.name) private readonly bookingModel: Model<bookingDocument>,
  ) {}



  
 private isMultipleOf15Minutes(startTime: string, endTime: string): boolean {
    const start = new Date(`1970-01-01T${startTime}:00`);
    const end = new Date(`1970-01-01T${endTime}:00`);
    const duration = (end.getTime() - start.getTime()) / (1000 * 60); // in minutes
    return duration % 15 === 0; // Check if the duration is a multiple of 15 minutes
  }

  private getCountOfMultiplesOf15(startTime: string, endTime: string): number {
    const start = new Date(`1970-01-01T${startTime}:00`);
    const end = new Date(`1970-01-01T${endTime}:00`);
    const durationInMinutes = (end.getTime() - start.getTime()) / (1000 * 60); // in minutes

    // Return the number of 15-minute multiples
    return Math.floor(durationInMinutes / 15);
}

async createBooking(dto: any, userId: string) {
    const checkVeternia = await this.userService.findById(dto.veterniaId);
    if (!checkVeternia) {
        return {
            success: false,
            message: "Veternia not found"
        };
    }

    // Check availability before creating booking
    const checkAvailability = await this.userService.isSlotAvailable(
        dto.veterniaId,
        dto.date,
        dto.startTime,
        dto.endTime,
    );

    if (!checkAvailability.available) {
        throw new ConflictException({
            success: false,
            message: checkAvailability.message || 'Slot not available',
        });
    }

    // Use UTC timezone consistently
    const start = new Date(`${dto.date}T${dto.startTime}:00Z`);
    const end = new Date(`${dto.date}T${dto.endTime}:00Z`);
    
    const duration = (end.getTime() - start.getTime()) / (1000 * 60); // in minutes
    
    if (duration % 15 !== 0) {
        return {
            success: false,
            message: 'Booking time should be in multiples of 15 minutes',
        };
    }

    // FIX: Add additional check right before saving to prevent race conditions
    const finalAvailabilityCheck = await this.userService.isSlotAvailable(
        dto.veterniaId,
        dto.date,
        dto.startTime,
        dto.endTime,
    );

    if (!finalAvailabilityCheck.available) {
        throw new ConflictException({
            success: false,
            message: 'Slot was booked by another user. Please try again.',
        });
    }

    if (duration <= 0) {
        return {
            success: false,
            message: 'Booking duration should be greater than 0 minutes',
        };
    }

    const multiplesOf15 = this.getCountOfMultiplesOf15(dto.startTime, dto.endTime);
    const user = await this.userModel.findById(userId);

    let amountToCharge = 0;

    if (multiplesOf15 === 1) {
        if (user.freeTrialSlots > 0) {
            // consume free trial
            user.freeTrialSlots -= 1;
            await user.save();
        } else {
            // free trial exhausted → charge
            amountToCharge = calculateCharges(multiplesOf15, checkVeternia.Charges.toString());
            console.log("amountToCharge",amountToCharge);
            // 🔹 Call your payment service here (Stripe/PayPal etc.)
            // const paymentIntent = await this.paymentService.createPaymentIntent({
            //     userId,
            //     amount: amountToCharge,
            //     currency: "usd", // or dto.currency
            // });

            // if (!paymentIntent) {
            //     return {
            //         success: false,
            //         message: 'Payment failed. Please try again.',
            //     };
            // }
        }
    } else {
        // always charge for multiple slots
        amountToCharge = calculateCharges(multiplesOf15, checkVeternia.Charges.toString());
        console.log("amountToCharge",amountToCharge);

        // const paymentIntent = await this.paymentService.createPaymentIntent({
        //     userId,
        //     amount: amountToCharge,
        //     currency: "usd",
        // });

        // if (!paymentIntent) {
        //     return {
        //         success: false,
        //         message: 'Payment failed. Please try again.',
        //     };
        // }
    }

    // Create booking
    const newBooking = new this.bookingModel({
        userId: userId,
        veterniaId: dto.veterniaId,
        petId: dto.petId,
        startTime: start,
        endTime: end,
        duration: duration,
        amountCharged: amountToCharge,
    });

    try {
        const savedBooking = await newBooking.save();
        return {
            success: true,
            message: 'Booking created successfully',
            booking: savedBooking,
        };
    } catch (error) {
        if (error.code === 11000) {
            throw new ConflictException({
                success: false,
                message: 'Slot was booked by another user. Please try another slot.',
            });
        }
        throw error;
    }
}


  // async createBooking(dto: any, userId: string) {
  
  //       const checkVeternia=await this.userService.findById(dto.veterniaId);
  //       if(!checkVeternia){
  //           return {
  //               success: false,
  //               message: "Veternia not found"
  //             };
  //       }
  //     const checkAvailability = await this.userService.isSlotAvailable(
  //       dto.veterniaId,
  //       dto.date,
  //       dto.startTime,
  //       dto.endTime,
  //     );

  //     if (!checkAvailability.available) {
  //       throw new ConflictException({
  //           success: false,
  //           message: checkAvailability.message || 'Slot not available',
  //         });
        
  //     }

      
  //     const start = new Date(`${dto.date}T${dto.startTime}:00`);
  //     const end = new Date(`${dto.date}T${dto.endTime}:00`);
  //     const duration = (end.getTime() - start.getTime()) / (1000 * 60); // minutes

  //    if(duration<=15){

     
  //     const newBooking = new this.bookingModel({
  //       userId: userId,              
  //       veterniaId: dto.veterniaId, 
  //       petId: dto.petId,
  //       startTime: start,
  //       endTime: end,
  //       duration: duration,
       
  //     });
      
  //     const savedBooking = await newBooking.save();

  //     return {
  //       success: true,
  //       message: 'Booking created successfully',
  //       booking: savedBooking,
  //     };
  //   }
  //   // add payment here according to duration
  //   if(duration>15){
  //     const newBooking = new this.bookingModel({
  //       userId: userId,              
  //       veterniaId: dto.veterniaId, 
  //       petId: dto.petId,
  //       startTime: start,
  //       endTime: end,
  //       duration: duration,
       
  //     });
  //     const savedBooking = await newBooking.save();
  //     return {
  //       success: true,
  //       message: 'Booking created successfully for duration above 15 minutes',
  //       booking: savedBooking,
  //     };
  //   }

  //   else{
  //     return {
  //       success: false,
  //       message: 'Booking duration should be greater than 15 minutes',
  //     };
  //   }
  //   // const amountToCharge=findCharges(duration,checkVeternia.Charges.toString());
  //   // const paymentIntent=await this.paymentService.createPaymentIntent()
  // }
  async getBookingOfCustomer(dto, userId) {
   
      // 1. Find the customer
      const findUser = await this.userService.findById(userId);
      if (!findUser) {
        throw new Error('User not found. Please enter correct Id');
      }
  
      console.log(userId)
      const findBooking = await this.bookingModel
        .find({
          userId: userId,
          bookingStatus: dto.status,
        })
        .populate({
          path: 'veterniaId', 
          select: 'userName email phoneNumber Specialization Charges State Experience WorkingHours about profilePicUrl availability', // Select only necessary fields
        })
      //  .select('startTime endTime duration bookingStatus veterniaId');
        
      return {
        success: true,
        total: findBooking.length,
        bookings: findBooking,
      };
    
  }

    async setBookingActive(bookingId: string) {
    const findBooking = await this.bookingModel.findById(bookingId);
    if (!findBooking) {
      throw new Error('Booking not found');
    }
    findBooking.bookingStatus = "active";
    await findBooking.save();
    return {
      success: true,
      message: 'Booking status updated to active successfully',
    };
  }

  async setBookingIncomplete(bookingId: string) {
    const findBooking = await this.bookingModel.findById(bookingId);
    if (!findBooking) {
      throw new Error('Booking not found');
    }
    findBooking.bookingStatus = "incomplete";
    await findBooking.save();
    return {
      success: true,
      message: 'Booking status updated to incomplete successfully',
    };
  }

  async setBookingCompleted(bookingId: string) {
    const findBooking = await this.bookingModel.findById(bookingId);
    if (!findBooking) {
      throw new Error('Booking not found');
    }
    findBooking.bookingStatus = "completed";
    await findBooking.save();
    return {
      success: true,
      message: 'Booking status updated to completed successfully',
    };
  }

  async bookingOfVeternia(dto:any,userId:any){
        
        const findUser = await this.userService.findById(userId);
        if (!findUser) {
          throw new Error('Veternia not found.');
        }
    
        const findBooking = await this.bookingModel
          .find({
            veterniaId: userId,
            bookingStatus: dto.status,
          }).populate({
            path:'petId'
          })
          .populate({
            path: 'veterniaId',     
            select: 'userName email phoneNumber Specialization Charges State Experience WorkingHours about profilePicUrl availability', // Select only necessary fields
          })
          .populate({
            path: 'userId',     
            select: 'userName profilePicUrl', // Select only necessary fields
          })
        //  .select('startTime endTime duration bookingStatus veterniaId');
          
        return {
          success: true,
          total: findBooking.length,
          bookings: findBooking,

        };
  }


  // ------------------  Veternia Home Section ----------------------------------
  async getAllBookingsCount(userId:string){
    try {
      const findUser = await this.userService.findById(userId);
      if (!findUser) {
        throw new Error('Veternia not found.');
      }

      if(findUser.userType!=="veternia"){
        throw new Error('User is not a veternia.');
      }

      const totalBookings = await this.bookingModel.countDocuments({ veterniaId: userId });
      return totalBookings;
    }
    catch (error) {
      throw new Error(`Error fetching total bookings: ${error.message}`);
    }
  }


  async getAllActiveBookingsCount(userId:string){
    try {
      const findUser = await this.userService.findById(userId);
      if (!findUser) {
        throw new Error('Veternia not found.');
      }
      if(findUser.userType!=="veternia"){
        throw new Error('User is not a veternia.');
      }

      const totalActiveBookings = await this.bookingModel.countDocuments({ veterniaId: userId,bookingStatus:"active" });
      return totalActiveBookings;
    }
    catch (error) {
      throw new Error(`Error fetching total active bookings: ${error.message}`);
    }
  }

  async getAllCompletedBookingsCount(userId:string){
    try {
      const findUser = await this.userService.findById(userId);
      if (!findUser) {
        throw new Error('Veternia not found.');
      }
      if(findUser.userType!=="veternia"){
        throw new Error('User is not a veternia.');
      }

      const totalCompletedBookings = await this.bookingModel.countDocuments({ veterniaId: userId,bookingStatus:"completed" });
      return totalCompletedBookings;
    }
    catch (error) {
      throw new Error(`Error fetching total completed bookings: ${error.message}`);
    }
  }

  async getAllIncompletedBookingsCount(userId:string){
    try {
      const findUser = await this.userService.findById(userId);
      if (!findUser) {
        throw new Error('Veternia not found.');
      }
      if(findUser.userType!=="veternia"){
        throw new Error('User is not a veternia.');
      }

      const totalIncompletedBookings = await this.bookingModel.countDocuments({ veterniaId: userId,bookingStatus:"incomplete" });
      return totalIncompletedBookings;
    }
    catch (error) {
      throw new Error(`Error fetching total incompleted bookings: ${error.message}`);
    }
  }

  async addBookingNotes(bookingId:string,notes:string){
      try {
      const findBooking = await this.bookingModel.findById(bookingId);
      if (!findBooking) {
        return {
          success: false,
          message: 'Booking not found',
        };
      }
      if (findBooking.bookingStatus !== "completed") {
        return {
          success: false,
          message: 'Session is not is not Completed yet',
        };
      }
      findBooking.notes = notes;
      await findBooking.save();
      return {
        success: true,
        message: 'Notes added successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: `Error adding notes: ${error.message}`,
      };
    }

  }
}
