import { ConflictException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import Stripe from 'stripe';
import { User,UserDocument } from 'src/modules/users/schemas/user.schema';
import { Model } from 'mongoose';
import * as fs from "fs";
@Injectable()
export class PaymentService {
    private stripe: Stripe;
    constructor(@InjectModel(User.name) private userModel: Model<UserDocument>,)//@InjectModel(User.name) Private userModel: Model<UserDocument>){
    {
        this.stripe = new Stripe(process.env.strip_secret_key as string, {
            apiVersion: '2025-08-27.basil',
          });
        
    }

async createConnectedAccount(email) {
  console.log("email in createConnectedAccount function", email);
  let account, accountLink;
  try {

    const existingUser = await this.userModel.findOne({ email: email });

    if (!existingUser) {
      throw new Error('No such user exists');
    }

    if (existingUser.connected_acc_id !== 'none' && existingUser.connected_external_acc_id !== 'none' && existingUser.payouts_enabled === true) {
      throw new Error('You already have a connected account');
    }

    if (existingUser.connected_acc_id !== 'none' && (existingUser.connected_external_acc_id === 'none' || existingUser.payouts_enabled === false)) {
      const response = await this.generateAccountLink(existingUser.connected_acc_id);
      if (response?.error) {
        throw new Error(response.error.message);
      }

      return {
        message: 'Connect account created',
        Connected_accountId: existingUser.connected_acc_id,
        Connected_externalAccountId: existingUser.connected_external_acc_id,
        onboardingUrl: response.onBoardingUrl,
      };
    } else {
      try {
        // enable transfers also
        console.log("Creating new account");
        account = await this.stripe.accounts.create({
          type: "express",
          country: "US",
          capabilities: {
            transfers: { requested: true },
            card_payments: { requested: true },
            // legacy_payments : {requested:true}
          },
          metadata: {
            email: email,
          },
        });
        accountLink = await this.stripe.accountLinks.create({
          account: account.id,
          refresh_url: process.env.SERVER_URL + "/payments/create-connected-account",
          return_url: "https://www.example.com",
          type: "account_onboarding",
        });

        // console.log("accountLink", accountLink);
        // console.log("account", account);
        // console.log("account.id", account.id);
        console.log("external_accounts", account.external_accounts);
        if (accountLink.url) {
          //log the externnal acc id
          console.log("account.external_accounts", account.external_accounts);
          await this.userModel.findOneAndUpdate(
            { email: email },
            {
              connected_acc_id: account.id,
              connected_external_acc_id: account.external_accounts ? account.external_accounts.data[0]?.id : null,
              payments_enabled: true,
            },
            { new: true }
          );
        }

      } catch (err) {
        const error = new Error(err);
        return {
          error: error.message,
        };
      }

      return { accountId: account.id, onBoardingUrl: accountLink.url , externalAccountId: account.external_accounts};
    }
  } catch (err) {
    const error = new Error(err);
    return {
      error: error.message,
    };
  }
}


    async payoutTheAmount (connected_acc_id: string, connected_external_acc_id: string, amount: number) {
      try {

        if(!connected_external_acc_id){
          throw new Error('External account id is required');
        }
        if(!connected_acc_id){
          throw new Error('Connected account id is required');
        }
        if(!amount){
          throw new Error('Amount is required');
        }
        // // store the external account id in user document 
        // await this.userModel.findOneAndUpdate(
        //   { connected_acc_id: connected_acc_id },
        //   {
        //     connected_external_acc_id: connected_external_acc_id,
        //   },
        //   { new: true }
        // );

        console.log("connected_external_acc_id", connected_external_acc_id);
        console.log("connected_acc_id", connected_acc_id);

        const payout = await this.stripe.payouts.create(
          {
            amount: Number((amount * 100).toFixed(0)),
            currency: "usd",
            destination: connected_external_acc_id,
          },
          {
            stripeAccount: connected_acc_id,
          }
        );
        return {
          payout: payout,
        };
      } catch (err) {
        const error = new Error(err);
        return {
          error: error.message,
        };
      }
    };

    async createStripeConnect(userId: string){
        const account= await this.stripe.accounts.create({
            type:'express',
            capabilities:{
                card_payments:{requested:true},
                transfers:{requested:true}
            },
            metadata:{userId}
        });
        await this.userModel.findByIdAndUpdate(userId,{
            stripeConnectId:account.id,
        },{new:true})
        return account;
    }

     // Function to fetch balance (incoming/received amounts)
    async getBalance(connected_acc_id: string) {
      try {
        const balance = await this.stripe.balance.retrieve({
          stripeAccount: connected_acc_id,
        });

        return {
          availableBalance: balance.available, // Incoming/Available funds
          pendingBalance: balance.pending,     // Pending funds
        };
      } catch (error) {
        return { error: 'Failed to fetch balance: ' + error.message };
      }
    }

    // Function to fetch payout information (received funds)
    async getPayouts(connected_acc_id: string) {
      try {
        const payouts = await this.stripe.payouts.list({
          stripeAccount: connected_acc_id,
          //limit: 10, // You can adjust this as per your need
        });

        return payouts.data.map(payout => ({
          amount: payout.amount / 100,  // Amount in USD
          currency: payout.currency.toUpperCase(),
          status: payout.status,
          created: new Date(payout.created * 1000),  // Convert to human-readable date
        }));
      } catch (error) {
        return { error: 'Failed to fetch payouts: ' + error.message };
      }
    }

    async getConnectedAccountInfo(accountId: string) {
  try {
    const account = await this.stripe.accounts.retrieve(accountId);

    console.log("Full Account Info:", account);

    return {
      id: account.id,
      email: account.email,
      type: account.type,
      country: account.country,
      business_type: account.business_type,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      capabilities: account.capabilities,
      requirements: account.requirements, // shows missing docs/info
    };
  } catch (err) {
    console.error("Error retrieving account:", err);
    throw new Error("Could not fetch account info");
  }
}


// how Do i attache card info in payment intent 
    async createPaymentIntent(dto: {
        userId: string;
        amount: number;
        currency: string;
        paymentMethodId: string;
      }) {
        const findUser = await this.userModel.findById(dto.userId);
        if (!findUser) {
          throw new ConflictException({
            success: false,
            message: 'User not found',
          });
        }
    
        if (!findUser.connected_acc_id) {
          throw new ConflictException({
            success: false,
            message: 'User does not have a connected Stripe account',
          });
        }
    
        // Create a PaymentIntent and attach the payment method
        const paymentIntent = await this.stripe.paymentIntents.create({
          amount: dto.amount * 100, // Amount in cents
          currency: dto.currency,
          transfer_data: {
            destination: findUser.connected_acc_id,
          },
          metadata: {
            userId: dto.userId,
          },
          payment_method: dto.paymentMethodId, // Attach the payment method ID here
          //confirm: true, // Confirm the payment immediately
        });
        
        findUser.allowed_withdrawl_balance += dto.amount;
        await findUser.save();

        console.log("paymentIntent",paymentIntent);
        return {
          clientSecret: paymentIntent.client_secret,
          paymentIntentId: paymentIntent.id,
        };
      }


    async  getPaymentInfo(paymentIntentId) {
    try {
        // Retrieve the PaymentIntent details
        const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

        console.log('Payment Intent details:', paymentIntent);

        return {
            id: paymentIntent.id,
            amount: paymentIntent.amount, // Amount in cents
            currency: paymentIntent.currency,
            status: paymentIntent.status,
            payment_method: paymentIntent.payment_method,
            created: paymentIntent.created, // Timestamp of creation
            description: paymentIntent.description,
            metadata: paymentIntent.metadata, // Additional metadata like userId
            transfer_data: paymentIntent.transfer_data,
            // You can include any other relevant fields here
        };
    } catch (error) {
        console.error('Error retrieving payment intent:', error);
        return { error: 'Failed to retrieve payment info: ' + error.message };
    }
}


async setAccountCapabilitiesActive(connected_acc_id: string) {
  try {
    // 1. Retrieve current account info
    const account = await this.stripe.accounts.retrieve(connected_acc_id);

    console.log("Before capabilities check:", account.capabilities);
    console.log("Currently due:", account.requirements.currently_due);

    // 2. If the account still has requirements, create an onboarding link
    if (
      account.requirements.currently_due &&
      account.requirements.currently_due.length > 0
    ) {
      const accountLink = await this.stripe.accountLinks.create({
        account: connected_acc_id,
        refresh_url: process.env.SERVER_URL + "/payments/reauth",
        return_url: "https://www.example.com",
        type: "account_onboarding",
      });

      return {
        id: account.id,
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        capabilities: account.capabilities,
        requirements: account.requirements,
        onboardingUrl: accountLink.url, // send user back to Stripe
      };
    }

    // 3. If nothing is due, just return current account info
    return {
      id: account.id,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      capabilities: account.capabilities,
      requirements: account.requirements,
      onboardingUrl: null, // not needed
    };
  } catch (err: any) {
    console.error("Stripe API error:", err);
    throw err; // re-throw so caller can handle
  }
}

async addTestFundsToConnectedAccount(connected_acc_id: string, amount: number) {
  try {
    const transfer = await this.stripe.transfers.create({
      amount: amount * 100, // in cents
      currency: "usd",
      destination: connected_acc_id, // e.g. "acct_1S2t0nJz3DVy7OY3"
    });

    return {
      success: true,
      transfer,
    };
  } catch (err: any) {
    console.error("Error adding test funds:", err.message);
    throw new Error("Failed to add test funds");
  }
}


async confirmPaymentIntent(paymentIntentId, paymentMethodId) {
  try {
    // Confirm the PaymentIntent to mark it as successful
    const paymentIntent = await this.stripe.paymentIntents.confirm(paymentIntentId, {
      payment_method: paymentMethodId,
    });

    // Check if the PaymentIntent has been confirmed successfully
    if (paymentIntent.status === 'succeeded') {
      console.log(`Payment Intent ${paymentIntentId} has been confirmed and marked as succeeded.`);
      return {
        status: 'succeeded',
        paymentIntent: paymentIntent,
      };
    } else {
      console.log(`Payment Intent ${paymentIntentId} is still in status: ${paymentIntent.status}`);
      return {
        status: 'pending',
        paymentIntent: paymentIntent,
      };
    }
  } catch (error) {
    console.error('Error confirming Payment Intent:', error.message);
    return {
      error: 'Failed to confirm Payment Intent: ' + error.message,
    };
  }
}

  // Function to generate an account link for an existing connected account
  async generateAccountLink(connected_acc_id) {
    try {
          const  accountLink = await this.stripe.accountLinks.create({
          account: connected_acc_id,
          refresh_url: process.env.SERVER_URL + "/payments/create-connected-account",
          return_url: "https://www.example.com",
          type: "account_onboarding",
        });

      return {
        onBoardingUrl: accountLink.url,
      };
    } catch (error) {
      return { error: error };
    }
  }

  async handleWebhook(event: Stripe.Event): Promise<any> {
    try {
      switch (event.type) {
        case 'account.updated':
          console.log("account.updated");
          await this.handleAccountUpdated(event);
          break;

        case 'account.external_account.created':
          console.log("account.external_account.created");
          await this.handleExternalAccountCreated(event);
          break;

        // Uncomment and modify these cases if you wish to handle other events
        /*
        case 'transfer.created':
          await this.handleTransferCreated(event);
          break;

        case 'payout.created':
          await this.handlePayoutCreated(event);
          break;

        case 'payout.updated':
          await this.handlePayoutUpdated(event);
          break;
        */

        default:
          console.log(`Unhandled event type ${event.type}`);
      }
    } catch (error) {
      console.error('Error processing event:', error);
      throw new Error(`Error processing event: ${event.type} - ${error.message}`);
    }
  }


  private async handleAccountUpdated(event: Stripe.Event) {
    const account = event.data.object as Stripe.Account;
    const user = await this.userModel.findOne({ email: account.metadata.email });

    if (!user) {
      console.log('No user exists');
      return;
    }

    user.connected_acc_id = account.id;
    user.payouts_enabled = account.payouts_enabled;
    await user.save();
  }

  private async handleExternalAccountCreated(event: Stripe.Event) {
    const externalAcc = event.data.object as Stripe.ExternalAccount;
    const user = await this.userModel.findOne({ connected_acc_id: externalAcc.account });

    if (!user) {
      console.log('No user exists with this connected account id');
      return;
    }
    console.log("externalAcc in handleExternalAccountCreated", externalAcc);
    user.connected_external_acc_id = externalAcc.id;
    user.payouts_enabled = true;
    await user.save();
  }

  // Uncomment and modify these methods if you need to handle transfers and payouts
  
  // private async handleTransferCreated(event: Stripe.Event) {
  //   const transfer = event.data.object as Stripe.Transfer;
  //   const receiverUser = await this.userModel.findOne({ connected_acc_id: transfer.destination });

  //   if (!receiverUser) {
  //     console.log('No such receiver user exists');
  //     return;
  //   }

  //   receiverUser.current_balance += transfer.amount;
  //   await receiverUser.save();
  // }

  // private async handlePayoutCreated(event: Stripe.Event) {
  //   const payoutCreated = event.data.object as Stripe.Payout;
  //   const payoutId = payoutCreated.id;
  //   const sentAmount = payoutCreated.amount;
  //   const destinationExternalAcc = payoutCreated.destination;

  //   const destinationUser = await this.userModel.findOne({ connected_external_acc_id: destinationExternalAcc });

  //   if (!destinationUser) {
  //     console.log('No destination user exists');
  //     return;
  //   }

  //   destinationUser.allowed_withdrawl_balance -= sentAmount / 100;
  //   const newWithdrawl = new withdrawls_model({
  //     amount: sentAmount / 100,
  //     user: destinationUser._id,
  //     payout_id: payoutId,
  //     payout_status: payoutCreated.status,
  //   });

  //   await newWithdrawl.save();
  //   await destinationUser.save();
  // }

  // private async handlePayoutUpdated(event: Stripe.Event) {
  //   const payoutUpdated = event.data.object as Stripe.Payout;
  //   const payoutStatus = payoutUpdated.status;
  //   const payoutId = payoutUpdated.id;
  //   const destinationExternalAcc = payoutUpdated.destination;
  //   const sentAmount = payoutUpdated.amount;

  //   const destinationUser = await this.userModel.findOne({ connected_external_acc_id: destinationExternalAcc });

  //   if (!destinationUser) {
  //     console.log('No destination user exists');
  //     return;
  //   }

  //   destinationUser.allowed_withdrawl_balance -= sentAmount / 100;
  //   const withdrawl = await withdrawls_model.findOne({ payout_id: payoutId });

  //   if (withdrawl) {
  //     withdrawl.payout_status = payoutStatus;
  //     await withdrawl.save();
  //   }

  //   await destinationUser.save();
  // }
  

  // async createStripeConnectedAcc(userId: string) {
  //   try {
  //     const user = await this.userModel.findById(userId);

  //     if (!user) {
  //       throw new Error('No such user exists');
  //     }

  //     if (user.connected_acc_id !== 'none' && user.connected_external_acc_id !== 'none' && user.payouts_enabled === true) {
  //       throw new Error('You already have a connected account');
  //     }

  //     if (user.connected_acc_id !== 'none' && (user.connected_external_acc_id === 'none' || user.payouts_enabled === false)) {
  //       const response = await generateAccountLink(user.connected_acc_id);
 
  //       if (response?.error) {
  //         throw new Error(response.error.message);
  //       }

  //       return {
  //         message: 'Connect account created',
  //         onboardingUrl: response.onBoardingUrl,
  //       };
  //     } else {
  //       const response = await createConnectedAccount(user.email);

  //       if (response?.error) {
  //         throw new Error(response.error.message);
  //       }

  //       return {
  //         message: 'Connect account created',
  //         onboardingUrl: response.onBoardingUrl,
  //       };
  //     }
  //   } catch (error) {
  //     throw new Error(error.message);
  //   }
  // }

}
