import { Module,forwardRef } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { UsersModule } from 'src/modules/users/users.module';
import mongoose from 'mongoose';
import { MongooseModule, Schema } from '@nestjs/mongoose';
import { User, UserSchema } from 'src/modules/users/schemas/user.schema';
import { PaymentController } from './payment.controller';

@Module({

  exports:[PaymentService],
  imports:[MongooseModule.forFeature([ { name: User.name, schema: UserSchema }]),forwardRef(() => UsersModule)],
  controllers: [PaymentController],
  providers: [PaymentService]

})
export class PaymentModule {}
