import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Types } from 'mongoose';

export type bookingDocument = booking & Document;

@Schema({ timestamps: true })
export class booking {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  veterniaId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Pet', required: true })
  petId: Types.ObjectId;

  @Prop({ required: true })
  startTime: Date;

  @Prop({ required: true })
  endTime: Date;
  
  @Prop({enum:["active","incomplete","completed"],default:"active"})
  bookingStatus: string
  
  @Prop({ required: true })
  duration: number;
  
  @Prop({default:false})
  paymentStatus:Boolean

  // -------- Notes ---------
  @Prop({default:""})
  notes:string

  // ------ Video calling ---------
    @Prop({ required: true })
    appId: string;
  
    @Prop({ required: true })
    channelName: string;
  
    @Prop({ required: true })
    token: string;
  
}


export const bookingSchema = SchemaFactory.createForClass(booking);
