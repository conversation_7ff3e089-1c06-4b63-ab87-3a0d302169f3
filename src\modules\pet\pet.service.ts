import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Pet } from './schemas/petSchema';
import { petDocument } from './schemas/petSchema';
import { Model,Types } from 'mongoose';
import { petSchema } from './schemas/petSchema';
import { createPetDto } from './dto/createPet.dto';
import { UploadService } from '../upload/upload.service';
import { PetLog, PetLogDocument } from './schemas/petLogSchema';
import { PetLogDto } from './dto/createPetLogs.dto';
import { EditLogDto } from './dto/editLog.dto';
import { bookingSchema,bookingDocument,booking } from '../booking/schemas/bookingSchemas';
import { NotFoundException, InternalServerErrorException } from '@nestjs/common';

@Injectable()
export class PetService {
  constructor(
    @InjectModel(Pet.name) private petModel: Model<petDocument>,
    @InjectModel(PetLog.name) private petLogModel: Model<PetLogDocument>,
   // @InjectModel(booking.name) private bookingModel:Model<bookingDocument>,
    private readonly uploadService:UploadService
  ) {}

  async createPet(createPetDto: createPetDto, file?: Express.Multer.File): Promise<petDocument> {
    if(file){
        const fileUrl=await this.uploadService.uploadFiles(file);
        createPetDto.image=fileUrl;
      console.log("file",fileUrl)
    }
    const createdPet = new this.petModel(createPetDto);
    return createdPet.save();
  }

  
  async getPets(userId: string): Promise<petDocument[]> {
    try {
    
      const pets = await this.petModel
        .find({ ownerId: userId })
        .populate('petLogs')
        .exec();
  
     
      const petWithAvgHealth = pets.map((pet) => {
        const logs = (pet.petLogs || []) as any[];
  
        const healthValues = logs
          .map((log) => log.health)
          .filter((h) => typeof h === 'number') as number[];
  
        const totalHealth = healthValues.reduce((sum, h) => sum + h, 0);
        const avgHealth =
          healthValues.length > 0 ? totalHealth / healthValues.length : 0;
  
        
        pet.healthStats = avgHealth;
  
        return pet;
      });
  
      return petWithAvgHealth;
    } catch (error) {
      console.error('Error fetching pets:', error);
      throw new Error('Unable to fetch pets');
    }
  }

  async getPetById(petId: string) {
    try {

      if(!petId){
        throw new NotFoundException('Please enter petId');
      }
      const pet = await this.petModel.findById(petId);
      if (!pet) {
        throw new NotFoundException('Pet not found');
      }
      return pet;
    } catch (error) {
      throw new InternalServerErrorException('Unable to fetch pet');
    }
  }
  
  async getPetLogById(petLogId: string) {
   
      const getPet = await this.petLogModel.findById(petLogId).populate({
        path: 'petId'
      });
      
      if (!getPet) {
        throw new NotFoundException('Pet log not found');
      }
      
      return getPet;
      
  }
  
  
async addPetLog(createPetLogDto: PetLogDto): Promise<{message: string, pet?: petDocument, petLog?: PetLogDocument, error?: string}> {
  try {
    const petExists = await this.petModel.findById(createPetLogDto.petId);
    if (!petExists) {
      return { message: 'Error', error: 'Pet not found' }; // Return error message
    }

    // Check if pet log for today's date exists
    const checkLog = await this.petLogModel.findOne({
      petId: createPetLogDto.petId,
      date: createPetLogDto.date,
    });
    if (checkLog) {
      return { message: 'Error', error: 'Pet log already exists for this date' }; // Return error message
    }

    // Create new pet log
    const newPetLog = new this.petLogModel(createPetLogDto);
    const savedLog: PetLogDocument = await newPetLog.save();

    // Update pet document
    petExists.petLogs.push(savedLog._id as Types.ObjectId);
    const updatePet = await petExists.save();

    return {
      message: "Pet log added successfully",
      pet: updatePet,
      petLog: savedLog
    };

  } catch (error) {
    return { message: 'Error', error: error.message || 'An error occurred' }; // Catch unexpected errors and return them
  }
}

  async editPet(petId: string, updateData: Partial<createPetDto>, file?: Express.Multer.File): Promise<petDocument> {
      const pet = await this.petModel.findById(petId);
      if (!pet) {
        throw new NotFoundException('Pet not found');
      }

      if (file) {
        const fileUrl = await this.uploadService.uploadFiles(file);
        updateData.image = fileUrl;
      }

      Object.assign(pet, updateData);
      return await pet.save();
    
  }

  async deletePet(petId: string): Promise<{ message: string }> {
   
      const deleted = await this.petModel.findByIdAndDelete(petId);
      if (!deleted) {
        throw new NotFoundException('Pet not found');
      }

      // Optionally also remove logs associated with this pet
      await this.petLogModel.deleteMany({ petId });

      return { message: 'Pet deleted successfully' };

  }
  // async getPetLogs(petId: string,date?: string): Promise<PetLogDocument[]> {
  //   return this.petLogModel.find({ petId }).sort({ createdAt: -1 }).exec();
  // }
  async getPetLogs(petId: string, date?: string): Promise<PetLogDocument[]> {
    const query: any = { petId };
  
    if (date) {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
  
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);
  
      query.date = { $gte: startOfDay, $lte: endOfDay };
    }
  
    return this.petLogModel
      .find(query)
      .sort({ createdAt: -1 }).populate('petId')
      .exec();
  }
  
  
  async editLogs(petLogId: string, editLogDto: EditLogDto) {
    
      const updatedLog = await this.petLogModel.findByIdAndUpdate(
        petLogId,
        { $set: editLogDto },
        { new: true }
      );
  
      if (!updatedLog) {
        throw new Error('Pet log not found');
      }
      // return the pet details
      const pet = await this.petModel.findById(updatedLog.petId);
  
      return { message: 'Pet log updated successfully', pet: pet, petLog: updatedLog };
   
  }
  


  async getCalenderData(petId: string, data: Date): Promise<Record<string, Record<string, PetLogDocument[]>>> {
    const startOfMonth = new Date(data.getFullYear(), data.getMonth(), 1);
    const endOfMonth = new Date(data.getFullYear(), data.getMonth() + 1, 0, 23, 59, 59, 999);
  
    try {
      const logs: (PetLog & { createdAt: Date })[] = await this.petLogModel
        .find({
          petId: petId,
          createdAt: {
            $gte: startOfMonth,
            $lte: endOfMonth,
          },
        })
       
  
      const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const monthName = data.toLocaleString('default', { month: 'long' }); // e.g., "August"
  
      const result: Record<string, Record<string, PetLogDocument[]>> = {
        [monthName]: {}
      };
  
      for (const log of logs) {
        const logDate = new Date(log.createdAt);
        const dayName = weekdays[logDate.getDay()];
  
        if (!result[monthName][dayName]) {
          result[monthName][dayName] = [];
        }
  
        result[monthName][dayName].push(log as any);
      }
  
      return result;
    } catch (error) {
      console.error('Error fetching calendar data:', error);
      throw new Error('Unable to fetch pet logs for calendar view');
    }
  }

  
}
