import { 
    <PERSON>, 
    Post, 
    Get, 
    Patch, 
    Body, 
    Param, 
    Query,
    UseGuards 
} from '@nestjs/common';
import { VideoCallingService } from './video-calling.service';
import { CreateVideoCallDto } from './dto/create-video-call.dto';
import { JoinVideoCallDto } from './dto/join-video-call.dto';
import { EndVideoCallDto } from './dto/end-video-call.dto';

@Controller('video-calling')
export class VideoCallingController {
    constructor(private readonly videoCallingService: VideoCallingService) {}

    @Post('create')
    async createVideoCall(@Body() createVideoCallDto: CreateVideoCallDto) {
        return this.videoCallingService.createVideoCall(createVideoCallDto);
    }

    @Post('join')
    async joinVideoCall(@Body() joinVideoCallDto: JoinVideoCallDto) {
        return this.videoCallingService.joinVideoCall(joinVideoCallDto);
    }

    @Patch('end')
    async endVideoCall(@Body() endVideoCallDto: EndVideoCallDto) {
        return this.videoCallingService.endVideoCall(endVideoCallDto);
    }

    @Get('history')
    async getVideoCallHistory(@Query('userId') userId?: string) {
        return this.videoCallingService.getVideoCallHistory(userId);
    }

    @Get('active/:channelName')
    async getActiveVideoCall(@Param('channelName') channelName: string) {
        return this.videoCallingService.getActiveVideoCall(channelName);
    }

    @Get('token/:channelName/:userId')
    async generateToken(
        @Param('channelName') channelName: string,
        @Param('userId') userId: string
    ) {
        return this.videoCallingService.joinVideoCall({ channelName, userId });
    }
}
