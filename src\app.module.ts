import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { ServicesModule } from './modules/services/services.module';
import { MailModule } from './mail/mail.module';
import { PetModule } from './modules/pet/pet.module';
import { AwsModule } from './modules/aws/aws.module';
import { AdminModule } from './modules/admin/admin.module';
import { BookingModule } from './modules/booking/booking.module';
import { SupportModule } from './modules/support/support.module';
import { PaymentModule } from './payment/payment.module';
import { VideoCallingModule } from './modules/videoCalling/video-calling.module';
import { RawBodyMiddleware } from './modules/middlewares/rawBodyMiddleware'; // <-- Import middleware

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGO_URI'),
      }),
      inject: [ConfigService],
    }),
    AuthModule,
    UsersModule,
    ServicesModule,
    MailModule,
    PetModule,
    AwsModule,
    AdminModule,
    BookingModule,
    SupportModule,
    PaymentModule,
    VideoCallingModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  // Apply RawBodyMiddleware for the webhook route
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RawBodyMiddleware).forRoutes('/payments/webhook'); // Apply it for the /payment/webhook route
  }
}
