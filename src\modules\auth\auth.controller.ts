import { AuthService } from './auth.service';
import { Body, Controller, Patch, Post,Put } from '@nestjs/common';
import { SignupDto } from './dto/signup.dto';
import { LoginDto } from './dto/login.dto';
import { verifyOtpDto } from './dto/verifyOtp.dto';
import { forgotPassDto } from './dto/forgotPass.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  async signup(@Body() dto: SignupDto) {
    return this.authService.signup(dto);
  }

  @Patch('verify-otp')
  async verifyOtp(@Body() dto: verifyOtpDto) {
    return this.authService.verifyOtp(dto);
  }

  @Post('login')
  async login(@Body() dto: LoginDto) {
    return this.authService.logIn(dto);
  }

  @Post('forgot-password')
  async forgotPass(@Body() dto: forgotPassDto) {
    return this.authService.forgotPass(dto);
  }
  @Put('verifyForgotOtp')
  async verifyForgotOtp(@Body() dto: verifyOtpDto)
  {
   return this.authService.verifyForgotOtp(dto) 
  }
  @Patch('reset-password')
  async resetPassword(
    @Body()
    dto: {
      userId: string;
     
      newPassword: string;
    },
  ) {
    return this.authService.resetPassword(dto);
  }
}
