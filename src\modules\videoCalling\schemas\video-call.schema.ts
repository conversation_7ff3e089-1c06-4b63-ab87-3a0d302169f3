import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document as MongooseDocument } from 'mongoose';

export type VideoCallDocument = MongooseDocument & VideoCall;

@Schema({ timestamps: true })
export class VideoCall {
    @Prop({ required: true })
    appId: string;

    @Prop({ required: true })
    channelName: string;

    @Prop({ required: true })
    token: string;

    @Prop({ required: false })
    userId: string;

    @Prop({ required: false })
    veterinarianId: string;

    @Prop({ enum: ['active', 'ended', 'scheduled'], default: 'scheduled' })
    status: string;

    @Prop({ required: false })
    startTime: Date;

    @Prop({ required: false })
    endTime: Date;

    @Prop({ required: false })
    duration: number; // in minutes
}

export const VideoCallSchema = SchemaFactory.createForClass(VideoCall);
