import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { VideoCallingController } from './video-calling.controller';
import { VideoCallingService } from './video-calling.service';
import { VideoCall, VideoCallSchema } from './schemas/video-call.schema';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: VideoCall.name, schema: VideoCallSchema }
        ])
    ],
    controllers: [VideoCallingController],
    providers: [VideoCallingService],
    exports: [VideoCallingService]
})
export class VideoCallingModule {}
