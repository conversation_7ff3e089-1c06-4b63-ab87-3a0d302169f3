import { Injectable,ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { SignupDto } from './dto/signup.dto';
import { LoginDto } from './dto/login.dto';
import { UsersService } from '../users/users.service';
import { generateOtp } from '../../shared/utils/generateOtp';
import { MailService } from '../../mail/mail.service';
import { Otp,OtpSchema } from '../users/schemas/otp.schema';
import * as bcrypt from "bcrypt";
import { verifyOtpDto } from './dto/verifyOtp.dto';
import { forgotPassDto } from './dto/forgotPass.dto';
import { UserDocument } from '../users/schemas/user.schema';
//import { PaymentService } from '../../payment/payment.service';
@Injectable()
export class AuthService {
    constructor(
        private readonly userService: UsersService,
        //private readonly jwtService: JwtService,
        private readonly jwtService: JwtService,
        private readonly mailService:MailService,
       // private readonly paymentService:PaymentService,
    //     @Inject(forwardRef(() => paymentService))
    // private commonService: ,

      ) {}
    
    async signup(dto: SignupDto) {
        const existingUser = await this.userService.findByEmail(dto.email);
        const otp = generateOtp();
        const expireAt = new Date(Date.now() + 10 * 60 * 1000)

        if(dto.userType === 'admin'){
          throw new ConflictException('Admin signup is not allowed.');
        }
   
        if (existingUser) {
          if (existingUser.isVerified) {
            throw new ConflictException("Email already exists");
          }
    
          await this.handleOtpResend((existingUser as any)._id.toString(), dto.email, otp, expireAt);
          
          return {
            message: "User already exists but not verified. OTP resent.",
            user: existingUser,
            password:dto.password
          };
        }
    
        const hashed = await bcrypt.hash(dto.password, 10);
        const newUser = await this.userService.create({ ...dto, password: hashed });
    
        await this.handleOtpResend(newUser._id.toString(), dto.email, otp, expireAt);
    
        return { message: "User created", user: newUser };
      }
      async verifyOtp(dto: verifyOtpDto) {
        const user = await this.userService.findById(dto.userId) as UserDocument;
        if (!user) {
          throw new ConflictException('User not found');
        }
        
        const isValid = await this.userService.verifyOtp(dto.userId, dto.otp);
        if (!isValid) {
          throw new ConflictException('Invalid or expired OTP');
        }

        const payload = {
          sub: user._id,
          email: user.email,
          userName: user.userName,
          userType:user.userType,
        };
    
        const accessToken = this.jwtService.sign(payload, {
          secret: process.env.JWT_SECRET,
          expiresIn: '30d',
        });
    
      
        return { message: 'OTP verified successfully',accessToken:accessToken };
      }

      async forgotPass(dto: forgotPassDto) {
        const findUser = await this.userService.findByEmail(dto.email);
      
        if (!findUser) {
          throw new ConflictException('User not found');
        }
      
        const otp = generateOtp();
        const expireAt = new Date(Date.now() + 2 * 60 * 1000);
      
        await this.handleOtpResend((findUser as any)._id.toString(), dto.email, otp, expireAt);
      
        return {
          message: 'OTP sent to email for password reset',
          user:findUser
        };
      }
      async resetPassword(dto: { userId: string;  newPassword: string }) {
        const user = await this.userService.findById(dto.userId);
        if (!user) {
          throw new ConflictException('User not registered');
        }
      
        const hashed = await bcrypt.hash(dto.newPassword, 10);
        await this.userService.updatePassword(dto.userId, hashed);
      
        return { message: 'Password reset successfully' };
      }
      async verifyForgotOtp(dto: verifyOtpDto) {
        const user = await this.userService.findById(dto.userId);
        if (!user) {
          throw new ConflictException('User not found');
        }
        console.log("dt",dto.userId,dto.otp)
        const isValid = await this.userService.verifyForgotOtp(dto.userId, dto.otp);
        console.log("isValid",isValid)
        if (!isValid) {
          throw new ConflictException('Invalid or expired OTP');
        }
      
        return { message: 'OTP verified successfully' };
      }

      async logIn(dto: LoginDto) {
        try {
          const user = await this.userService.findByEmail(dto.email as string);
      
          if (!user) {
            throw new ConflictException('User does not exist');
          }
      
          const match = await bcrypt.compare(dto.password as string, user.password as string);
      
          if (!match) {
            throw new ConflictException('Invalid credentials');
          }
      
          if (!user.isVerified) {
            throw new ConflictException(`Please verify your account before logging in. ${user.isVerified}`);
          }
          if (dto.userType && dto.userType !== user.userType) {
            throw new ConflictException(`Invalid user type. Expected: ${user.userType}`);
          }

          const payload = {
            sub: user._id,
            email: user.email,
            userName: user.userName,
            userType:user.userType
          };
      
          const accessToken = this.jwtService.sign(payload, {
            secret: process.env.JWT_SECRET,
            expiresIn: '30d',
          });
          if(user.userType === 'veternia' && !user.isComplete){
              return {
            message: 'Please complete your profile',
            accessToken,
            user: {
              id: user._id,
              email: user.email,
              userName: user.userName,
              isVerified:user.isVerified,
              profilePicUrl:user.profilePicUrl,
              userType:user.userType,
              isApprove:user.approve,
              blockStatus:user.blockStatus,
              isCompleted:user.isComplete,
              phoneNumber:user.phoneNumber
            },
            };
          }
          if(user.userType === 'veternia' && !user.approve){
            throw new ConflictException(`Your account is not approved yet. Please wait.`);
          }
          if(user.blockStatus){
            throw new ConflictException(`Your account is blocked. Please contact admin.`);
          }
          return {
            message: 'Login successful',
            accessToken,
            user: {
              id: user._id,
              email: user.email,
              userName: user.userName,
              isVerified:user.isVerified,
              profilePicUrl:user.profilePicUrl,
              userType:user.userType,
              isApprove:user.approve,
              blockStatus:user.blockStatus,
              isCompleted:user.isComplete,
              phoneNumber:user.phoneNumber
            },
          };
        } catch (error) {
          throw new ConflictException(error.message || 'Something went wrong during login');
        }
      }
      
      
      private async handleOtpResend(userId: string, email: string, otp: string, expireAt: Date) {
        await this.userService.deleteOtps(userId);
        await this.userService.createOtp(userId, otp, expireAt);
        await this.mailService.sendOtpEmail(email, otp);
      }

}
